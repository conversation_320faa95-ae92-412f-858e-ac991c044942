/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/main.ts
var main_exports = {};
__export(main_exports, {
  default: () => TaskSyncPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian3 = require("obsidian");

// src/services/VaultScannerService.ts
var import_obsidian = require("obsidian");
var VaultScanner = class {
  constructor(vault, settings) {
    this.vault = vault;
    this.settings = settings;
  }
  async scanTasksFolder() {
    return this.scanFolder(this.settings.tasksFolder);
  }
  async scanProjectsFolder() {
    return this.scanFolder(this.settings.projectsFolder);
  }
  async scanAreasFolder() {
    return this.scanFolder(this.settings.areasFolder);
  }
  async scanTemplatesFolder() {
    return this.scanFolder(this.settings.templateFolder);
  }
  async scanFolder(folderPath) {
    if (!folderPath) return [];
    try {
      const folder = this.vault.getAbstractFileByPath(folderPath);
      if (!folder || !this.isFolder(folder)) {
        return [];
      }
      const files = [];
      this.collectMarkdownFiles(folder, files);
      return files;
    } catch (error) {
      console.error(`Failed to scan folder ${folderPath}:`, error);
      return [];
    }
  }
  collectMarkdownFiles(folder, files) {
    for (const child of folder.children) {
      if (this.isFile(child) && child.extension === "md") {
        files.push(child.path);
      } else if (this.isFolder(child)) {
        this.collectMarkdownFiles(child, files);
      }
    }
  }
  isFolder(obj) {
    var _a;
    return obj && (obj instanceof import_obsidian.TFolder || ((_a = obj.constructor) == null ? void 0 : _a.name) === "TFolder" || obj.children !== void 0);
  }
  isFile(obj) {
    var _a;
    return obj && (obj instanceof import_obsidian.TFile || ((_a = obj.constructor) == null ? void 0 : _a.name) === "TFile" || obj.extension !== void 0);
  }
  async findTaskFiles() {
    const taskPaths = await this.scanTasksFolder();
    const taskFiles = [];
    for (const path of taskPaths) {
      try {
        const fileInfo = await this.getFileInfo(path);
        if (fileInfo) {
          taskFiles.push(fileInfo);
        }
      } catch (error) {
        console.error(`Failed to process task file ${path}:`, error);
      }
    }
    return taskFiles;
  }
  async findProjectFiles() {
    const projectPaths = await this.scanProjectsFolder();
    const projectFiles = [];
    for (const path of projectPaths) {
      try {
        const fileInfo = await this.getFileInfo(path);
        if (fileInfo) {
          const projectFile = {
            ...fileInfo,
            taskFiles: await this.findRelatedTaskFiles(path)
          };
          projectFiles.push(projectFile);
        }
      } catch (error) {
        console.error(`Failed to process project file ${path}:`, error);
      }
    }
    return projectFiles;
  }
  async findAreaFiles() {
    const areaPaths = await this.scanAreasFolder();
    const areaFiles = [];
    for (const path of areaPaths) {
      try {
        const fileInfo = await this.getFileInfo(path);
        if (fileInfo) {
          const areaFile = {
            ...fileInfo,
            projectFiles: await this.findRelatedProjectFiles(path)
          };
          areaFiles.push(areaFile);
        }
      } catch (error) {
        console.error(`Failed to process area file ${path}:`, error);
      }
    }
    return areaFiles;
  }
  async findTemplateFiles() {
    const templatePaths = await this.scanTemplatesFolder();
    const templateFiles = [];
    for (const path of templatePaths) {
      try {
        const fileInfo = await this.getFileInfo(path);
        if (fileInfo) {
          const templateFile = {
            ...fileInfo,
            templateType: this.detectTemplateType(path, fileInfo.content || ""),
            variables: this.extractTemplateVariables(fileInfo.content || "")
          };
          templateFiles.push(templateFile);
        }
      } catch (error) {
        console.error(`Failed to process template file ${path}:`, error);
      }
    }
    return templateFiles;
  }
  async findBaseFiles() {
    const allFiles = this.vault.getMarkdownFiles();
    const baseFiles = [];
    for (const file of allFiles) {
      if (file.extension === "base" || file.name.endsWith(".base.md")) {
        try {
          const content = await this.vault.read(file);
          const baseFile = {
            path: file.path,
            name: file.name,
            exists: true,
            lastModified: new Date(file.stat.mtime),
            size: file.stat.size,
            content,
            frontmatter: this.extractFrontmatter(content),
            viewType: this.detectBaseViewType(content),
            entityType: this.detectBaseEntityType(content),
            isValid: this.validateBaseFile(content),
            errors: this.getBaseFileErrors(content)
          };
          baseFiles.push(baseFile);
        } catch (error) {
          console.error(`Failed to process base file ${file.path}:`, error);
        }
      }
    }
    return baseFiles;
  }
  async validateFolderStructure() {
    const result = {
      isValid: true,
      errors: [],
      warnings: [],
      missingFolders: [],
      suggestions: []
    };
    const foldersToCheck = [
      { path: this.settings.tasksFolder, name: "Tasks" },
      { path: this.settings.projectsFolder, name: "Projects" },
      { path: this.settings.areasFolder, name: "Areas" },
      { path: this.settings.templateFolder, name: "Templates" }
    ];
    for (const folder of foldersToCheck) {
      if (!folder.path) {
        result.warnings.push(`${folder.name} folder path is not configured`);
        continue;
      }
      const exists = await this.folderExists(folder.path);
      if (!exists) {
        result.missingFolders.push(folder.path);
        result.errors.push(`${folder.name} folder does not exist: ${folder.path}`);
        result.isValid = false;
      }
    }
    const paths = foldersToCheck.map((f) => f.path).filter(Boolean);
    const duplicates = paths.filter((path, index) => paths.indexOf(path) !== index);
    if (duplicates.length > 0) {
      result.errors.push(`Duplicate folder paths detected: ${duplicates.join(", ")}`);
      result.isValid = false;
    }
    if (result.missingFolders.length > 0) {
      result.suggestions.push('Use the "Create Missing Folders" command to automatically create missing folders');
    }
    return result;
  }
  async createMissingFolders() {
    const foldersToCreate = [
      this.settings.tasksFolder,
      this.settings.projectsFolder,
      this.settings.areasFolder,
      this.settings.templateFolder
    ].filter(Boolean);
    for (const folderPath of foldersToCreate) {
      try {
        const exists = await this.folderExists(folderPath);
        if (!exists) {
          await this.vault.createFolder(folderPath);
          console.log(`Created folder: ${folderPath}`);
        }
      } catch (error) {
        console.error(`Failed to create folder ${folderPath}:`, error);
      }
    }
  }
  async getFileInfo(path) {
    try {
      const file = this.vault.getAbstractFileByPath(path);
      if (!file || !this.isFile(file)) {
        return null;
      }
      const tfile = file;
      const content = await this.vault.read(tfile);
      return {
        path: tfile.path,
        name: tfile.name,
        exists: true,
        lastModified: new Date(tfile.stat.mtime),
        size: tfile.stat.size,
        content,
        frontmatter: this.extractFrontmatter(content)
      };
    } catch (error) {
      console.error(`Failed to get file info for ${path}:`, error);
      return null;
    }
  }
  async findRelatedTaskFiles(projectPath) {
    return [];
  }
  async findRelatedProjectFiles(areaPath) {
    return [];
  }
  detectTemplateType(path, content) {
    const pathLower = path.toLowerCase();
    if (pathLower.includes("task")) return "task";
    if (pathLower.includes("project")) return "project";
    if (pathLower.includes("area")) return "area";
    const contentLower = content.toLowerCase();
    if (contentLower.includes("deadline") || contentLower.includes("status")) return "task";
    if (contentLower.includes("objectives") || contentLower.includes("milestones")) return "project";
    return "task";
  }
  extractTemplateVariables(content) {
    const variables = [];
    const variableRegex = /\{\{([^}]+)\}\}/g;
    let match;
    while ((match = variableRegex.exec(content)) !== null) {
      const variable = match[1].trim();
      if (!variables.includes(variable)) {
        variables.push(variable);
      }
    }
    return variables;
  }
  detectBaseViewType(content) {
    return "kanban";
  }
  detectBaseEntityType(content) {
    return "task";
  }
  validateBaseFile(content) {
    return content.includes("```base") || content.includes("view:");
  }
  getBaseFileErrors(content) {
    const errors = [];
    if (!this.validateBaseFile(content)) {
      errors.push("Invalid base file format");
    }
    return errors;
  }
  extractFrontmatter(content) {
    const frontmatterRegex = /^---\n([\s\S]*?)\n---/;
    const match = content.match(frontmatterRegex);
    if (!match) return {};
    try {
      const frontmatterText = match[1];
      const lines = frontmatterText.split("\n");
      const result = {};
      for (const line of lines) {
        const colonIndex = line.indexOf(":");
        if (colonIndex > 0) {
          const key = line.substring(0, colonIndex).trim();
          const value = line.substring(colonIndex + 1).trim();
          result[key] = value;
        }
      }
      return result;
    } catch (error) {
      console.error("Failed to parse frontmatter:", error);
      return {};
    }
  }
  async folderExists(path) {
    try {
      const folder = this.vault.getAbstractFileByPath(path);
      return this.isFolder(folder);
    } catch (e) {
      return false;
    }
  }
};

// src/components/modals/TaskCreateModal.ts
var import_obsidian2 = require("obsidian");
var TaskCreateModal = class extends import_obsidian2.Modal {
  constructor(app, plugin, context = { type: "none" }) {
    super(app);
    this.formData = {};
    this.plugin = plugin;
    this.context = context;
    this.modalEl.addClass("task-sync-create-task");
    this.modalEl.addClass("task-sync-modal");
  }
  onOpen() {
    const title = this.getContextualTitle();
    this.titleEl.setText(title);
    this.initializeFormData();
    this.createContent();
  }
  getContextualTitle() {
    switch (this.context.type) {
      case "project":
        return `Create Task for Project: ${this.context.name}`;
      case "area":
        return `Create Task for Area: ${this.context.name}`;
      default:
        return "Create New Task";
    }
  }
  initializeFormData() {
    this.formData = {
      name: "",
      type: "Task",
      done: false,
      status: "Backlog",
      tags: []
    };
    if (this.context.type === "project" && this.context.name) {
      this.formData.project = this.context.name;
    } else if (this.context.type === "area" && this.context.name) {
      this.formData.areas = this.context.name;
    }
  }
  createContent() {
    this.contentEl.empty();
    const container = this.contentEl.createDiv("task-sync-modal-content");
    if (this.context.type !== "none") {
      this.createContextInfo(container);
    }
    this.createFormFields(container);
    this.createFormActions(container);
  }
  createContextInfo(container) {
    const contextDiv = container.createDiv("task-sync-context-info");
    const contextType = this.context.type === "project" ? "Project" : "Area";
    contextDiv.createEl("p", {
      text: `Creating task for ${contextType}: ${this.context.name}`,
      cls: "task-sync-context-text"
    });
  }
  createFormFields(container) {
    new import_obsidian2.Setting(container).setName("Task Name").setDesc("Enter a descriptive name for the task").addText((text) => {
      text.setPlaceholder("Enter task name...").setValue(this.formData.name || "").onChange((value) => {
        this.formData.name = value;
      });
      text.inputEl.addClass("task-sync-required-field");
    });
    new import_obsidian2.Setting(container).setName("Type").setDesc("Specify the type of task").addDropdown((dropdown) => {
      dropdown.addOption("Task", "Task").addOption("Milestone", "Milestone").addOption("Bug", "Bug").addOption("Feature", "Feature").setValue(this.formData.type || "Task").onChange((value) => {
        this.formData.type = value;
      });
    });
    new import_obsidian2.Setting(container).setName("Project").setDesc("Related project for this task").addText((text) => {
      text.setPlaceholder("Project name (optional)").setValue(this.formData.project || "").onChange((value) => {
        this.formData.project = value;
      });
      if (this.context.type === "project") {
        text.setDisabled(true);
      }
    });
    new import_obsidian2.Setting(container).setName("Areas").setDesc("Related areas for this task").addText((text) => {
      text.setPlaceholder("Area names (optional)").setValue(this.formData.areas || "").onChange((value) => {
        this.formData.areas = value;
      });
      if (this.context.type === "area") {
        text.setDisabled(true);
      }
    });
    new import_obsidian2.Setting(container).setName("Parent Task").setDesc("Parent task if this is a subtask").addText((text) => {
      text.setPlaceholder("Parent task name (optional)").setValue(this.formData.parentTask || "").onChange((value) => {
        this.formData.parentTask = value;
      });
    });
    new import_obsidian2.Setting(container).setName("Sub-tasks").setDesc("Comma-separated list of subtasks").addText((text) => {
      text.setPlaceholder("Subtask names (optional)").setValue(this.formData.subTasks || "").onChange((value) => {
        this.formData.subTasks = value;
      });
    });
    new import_obsidian2.Setting(container).setName("Tags").setDesc("Comma-separated tags for organization").addText((text) => {
      var _a;
      text.setPlaceholder("tag1, tag2, tag3").setValue(((_a = this.formData.tags) == null ? void 0 : _a.join(", ")) || "").onChange((value) => {
        this.formData.tags = this.parseTags(value);
      });
    });
    new import_obsidian2.Setting(container).setName("Status").setDesc("Current status of the task").addDropdown((dropdown) => {
      dropdown.addOption("Backlog", "Backlog").addOption("Todo", "Todo").addOption("In Progress", "In Progress").addOption("Done", "Done").setValue(this.formData.status || "Backlog").onChange((value) => {
        this.formData.status = value;
      });
    });
    new import_obsidian2.Setting(container).setName("Priority").setDesc("Task priority level").addDropdown((dropdown) => {
      dropdown.addOption("", "Select priority...").addOption("Low", "Low").addOption("Medium", "Medium").addOption("High", "High").addOption("Urgent", "Urgent").setValue(this.formData.priority || "").onChange((value) => {
        this.formData.priority = value;
      });
    });
    new import_obsidian2.Setting(container).setName("Description").setDesc("Detailed description of the task").addTextArea((text) => {
      text.setPlaceholder("Task description...").setValue(this.formData.description || "").onChange((value) => {
        this.formData.description = value;
      });
      text.inputEl.rows = 4;
    });
  }
  createFormActions(container) {
    const actionsContainer = container.createDiv("task-sync-form-actions");
    const cancelButton = actionsContainer.createEl("button", {
      text: "Cancel",
      type: "button",
      cls: "mod-cancel"
    });
    cancelButton.addEventListener("click", () => this.close());
    const submitButton = actionsContainer.createEl("button", {
      text: "Create Task",
      type: "button",
      cls: "mod-cta"
    });
    submitButton.addEventListener("click", () => this.handleSubmit());
  }
  async handleSubmit() {
    var _a;
    if (!((_a = this.formData.name) == null ? void 0 : _a.trim())) {
      this.showError("Task name is required");
      return;
    }
    const taskData = {
      name: this.formData.name.trim(),
      type: this.formData.type || void 0,
      areas: this.formData.areas || void 0,
      parentTask: this.formData.parentTask || void 0,
      subTasks: this.formData.subTasks || void 0,
      tags: this.formData.tags || [],
      project: this.formData.project || void 0,
      done: false,
      status: this.formData.status || "Backlog",
      priority: this.formData.priority || void 0,
      description: this.formData.description || void 0
    };
    try {
      if (this.onSubmitCallback) {
        await this.onSubmitCallback(taskData);
      }
      this.close();
    } catch (error) {
      console.error("Failed to create task:", error);
      this.showError("Failed to create task. Please try again.");
    }
  }
  showError(message) {
    let errorContainer = this.contentEl.querySelector(".task-sync-error");
    if (!errorContainer) {
      errorContainer = this.contentEl.createDiv("task-sync-error");
      errorContainer.style.color = "var(--text-error)";
      errorContainer.style.marginBottom = "1rem";
      errorContainer.style.padding = "0.5rem";
      errorContainer.style.backgroundColor = "var(--background-modifier-error)";
      errorContainer.style.borderRadius = "4px";
    }
    errorContainer.textContent = message;
    setTimeout(() => {
      if (errorContainer) {
        errorContainer.remove();
      }
    }, 5e3);
  }
  parseTags(tagsString) {
    return tagsString.split(",").map((tag) => tag.trim()).filter((tag) => tag.length > 0);
  }
  onSubmit(callback) {
    this.onSubmitCallback = callback;
  }
  onClose() {
  }
};

// src/main.ts
var DEFAULT_SETTINGS = {
  tasksFolder: "Tasks",
  projectsFolder: "Projects",
  areasFolder: "Areas",
  templateFolder: "Templates",
  useTemplater: false,
  defaultTaskTemplate: "",
  defaultProjectTemplate: "",
  defaultAreaTemplate: ""
};
var TaskSyncPlugin = class extends import_obsidian3.Plugin {
  async onload() {
    console.log("Loading Task Sync Plugin");
    await this.loadSettings();
    this.vaultScanner = new VaultScanner(this.app.vault, this.settings);
    this.addSettingTab(new TaskSyncSettingTab(this.app, this));
    this.addCommand({
      id: "add-task",
      name: "Add Task",
      callback: () => {
        this.openTaskCreateModal();
      }
    });
  }
  onunload() {
    console.log("Unloading Task Sync Plugin");
  }
  async loadSettings() {
    try {
      const loadedData = await this.loadData();
      this.settings = Object.assign({}, DEFAULT_SETTINGS, loadedData);
      await this.migrateSettings();
      this.validateSettings();
    } catch (error) {
      console.error("Task Sync: Failed to load settings:", error);
      this.settings = { ...DEFAULT_SETTINGS };
    }
  }
  async saveSettings() {
    try {
      await this.saveData(this.settings);
    } catch (error) {
      console.error("Task Sync: Failed to save settings:", error);
      throw error;
    }
  }
  async migrateSettings() {
  }
  validateSettings() {
    const folderFields = ["tasksFolder", "projectsFolder", "areasFolder", "templateFolder"];
    folderFields.forEach((field) => {
      if (typeof this.settings[field] !== "string") {
        console.warn(`Task Sync: Invalid ${field}, using default`);
        this.settings[field] = DEFAULT_SETTINGS[field];
      }
    });
  }
  // UI Methods
  async openTaskCreateModal() {
    try {
      const context = this.detectCurrentFileContext();
      const modal = new TaskCreateModal(this.app, this, context);
      modal.onSubmit(async (taskData) => {
        await this.createTask(taskData);
      });
      modal.open();
    } catch (error) {
      console.error("Failed to open task creation modal:", error);
    }
  }
  detectCurrentFileContext() {
    const activeFile = this.app.workspace.getActiveFile();
    if (!activeFile) {
      return { type: "none" };
    }
    const filePath = activeFile.path;
    const fileName = activeFile.name;
    if (filePath.startsWith(this.settings.projectsFolder + "/")) {
      return {
        type: "project",
        name: fileName.replace(".md", ""),
        path: filePath
      };
    }
    if (filePath.startsWith(this.settings.areasFolder + "/")) {
      return {
        type: "area",
        name: fileName.replace(".md", ""),
        path: filePath
      };
    }
    return { type: "none" };
  }
  // Task creation logic
  async createTask(taskData) {
    try {
      const taskFileName = `${taskData.name.replace(/[^a-zA-Z0-9\s]/g, "").replace(/\s+/g, "-")}.md`;
      const taskPath = `${this.settings.tasksFolder}/${taskFileName}`;
      const taskContent = this.generateTaskContent(taskData);
      await this.app.vault.create(taskPath, taskContent);
      console.log("Task created successfully:", taskPath);
    } catch (error) {
      console.error("Failed to create task:", error);
      throw error;
    }
  }
  generateTaskContent(taskData) {
    const frontmatter = [
      "---",
      `Title: ${taskData.name}`,
      `Type: ${taskData.type || "Task"}`,
      `Areas: ${taskData.areas || ""}`,
      `Parent task: ${taskData.parentTask || ""}`,
      `Sub-tasks: ${taskData.subTasks || ""}`,
      `tags: ${taskData.tags ? taskData.tags.join(", ") : ""}`,
      `Project: ${taskData.project || ""}`,
      `Done: ${taskData.done || false}`,
      `Status: ${taskData.status || "Backlog"}`,
      `Priority: ${taskData.priority || ""}`,
      "---",
      "",
      taskData.description || "Task description...",
      ""
    ];
    return frontmatter.join("\n");
  }
};
var TaskSyncSettingTab = class extends import_obsidian3.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.validationErrors = /* @__PURE__ */ new Map();
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.addClass("task-sync-settings");
    const header = containerEl.createDiv("task-sync-settings-header");
    header.createEl("h2", { text: "Task Sync Settings" });
    header.createEl("p", {
      text: "Configure your task management system. Changes are saved automatically.",
      cls: "task-sync-settings-description"
    });
    this.createTabbedInterface(containerEl);
  }
  createTabbedInterface(containerEl) {
    const tabContainer = containerEl.createDiv("task-sync-settings-tabs");
    const tabHeaders = tabContainer.createDiv("task-sync-settings-tab-headers");
    const tabContent = tabContainer.createDiv("task-sync-settings-tab-content");
    const tabs = [
      { id: "folders", label: "\u{1F4C1} Folders", content: () => this.createFolderSettings(tabContent) },
      { id: "templates", label: "\u{1F4DD} Templates", content: () => this.createTemplateSettings(tabContent) }
    ];
    let activeTab = "folders";
    tabs.forEach((tab, index) => {
      const tabHeader = tabHeaders.createEl("button", {
        text: tab.label,
        cls: `task-sync-settings-tab-header ${index === 0 ? "active" : ""}`
      });
      tabHeader.addEventListener("click", () => {
        tabHeaders.querySelectorAll(".task-sync-settings-tab-header").forEach((h) => h.removeClass("active"));
        tabHeader.addClass("active");
        activeTab = tab.id;
        tabContent.empty();
        tab.content();
      });
    });
    tabs[0].content();
  }
  createFolderSettings(container) {
    container.createEl("h3", { text: "Folder Configuration" });
    container.createEl("p", {
      text: "Specify where your tasks, projects, and areas will be stored in your vault.",
      cls: "task-sync-settings-section-desc"
    });
    this.createFolderSetting(
      container,
      "tasksFolder",
      "Tasks Folder",
      "Folder where task files will be stored",
      "Tasks"
    );
    this.createFolderSetting(
      container,
      "projectsFolder",
      "Projects Folder",
      "Folder where project files will be stored",
      "Projects"
    );
    this.createFolderSetting(
      container,
      "areasFolder",
      "Areas Folder",
      "Folder where area files will be stored",
      "Areas"
    );
    const infoBox = container.createDiv("task-sync-settings-info");
    infoBox.createEl("strong", { text: "Note: " });
    infoBox.appendText("Folders will be created automatically if they don't exist. Use relative paths from your vault root.");
  }
  createFolderSetting(container, key, name, desc, placeholder) {
    const setting = new import_obsidian3.Setting(container).setName(name).setDesc(desc).addText((text) => {
      text.setPlaceholder(placeholder).setValue(this.plugin.settings[key]).onChange(async (value) => {
        const validation = this.validateFolderPath(value);
        if (validation.isValid) {
          this.clearValidationError(key);
          this.plugin.settings[key] = value;
          await this.plugin.saveSettings();
        } else {
          this.setValidationError(key, validation.error);
        }
        this.updateSettingValidation(setting, key);
      });
    });
    this.updateSettingValidation(setting, key);
  }
  createTemplateSettings(container) {
    container.createEl("h3", { text: "Template Configuration" });
    container.createEl("p", {
      text: "Configure template integration for creating tasks, projects, and areas.",
      cls: "task-sync-settings-section-desc"
    });
    this.createFolderSetting(
      container,
      "templateFolder",
      "Template Folder",
      "Folder where templates are stored",
      "Templates"
    );
    new import_obsidian3.Setting(container).setName("Use Templater Plugin").setDesc("Enable integration with Templater plugin for advanced templates").addToggle((toggle) => toggle.setValue(this.plugin.settings.useTemplater).onChange(async (value) => {
      this.plugin.settings.useTemplater = value;
      await this.plugin.saveSettings();
    }));
    new import_obsidian3.Setting(container).setName("Default Task Template").setDesc("Default template to use when creating new tasks").addText((text) => text.setPlaceholder("task-template.md").setValue(this.plugin.settings.defaultTaskTemplate).onChange(async (value) => {
      this.plugin.settings.defaultTaskTemplate = value;
      await this.plugin.saveSettings();
    }));
    new import_obsidian3.Setting(container).setName("Default Project Template").setDesc("Default template to use when creating new projects").addText((text) => text.setPlaceholder("project-template.md").setValue(this.plugin.settings.defaultProjectTemplate).onChange(async (value) => {
      this.plugin.settings.defaultProjectTemplate = value;
      await this.plugin.saveSettings();
    }));
    new import_obsidian3.Setting(container).setName("Default Area Template").setDesc("Default template to use when creating new areas").addText((text) => text.setPlaceholder("area-template.md").setValue(this.plugin.settings.defaultAreaTemplate).onChange(async (value) => {
      this.plugin.settings.defaultAreaTemplate = value;
      await this.plugin.saveSettings();
    }));
  }
  createUISettings(container) {
    container.createEl("h3", { text: "Interface Settings" });
    container.createEl("p", {
      text: "Customize the appearance and behavior of the Task Sync interface.",
      cls: "task-sync-settings-section-desc"
    });
    const placeholder = container.createDiv("task-sync-settings-placeholder");
    placeholder.createEl("p", { text: "UI customization options will be available in a future update." });
  }
  createAdvancedSettings(container) {
    container.createEl("h3", { text: "Advanced Settings" });
    container.createEl("p", {
      text: "Advanced configuration options for power users.",
      cls: "task-sync-settings-section-desc"
    });
    const placeholder = container.createDiv("task-sync-settings-placeholder");
    placeholder.createEl("p", { text: "Advanced options will be available in a future update." });
  }
  // Validation methods
  validateFolderPath(path) {
    if (!path.trim()) {
      return { isValid: false, error: "Folder path cannot be empty" };
    }
    if (path.includes("..") || path.startsWith("/")) {
      return { isValid: false, error: "Invalid folder path" };
    }
    return { isValid: true };
  }
  validateSyncInterval(value) {
    const minutes = parseInt(value);
    if (isNaN(minutes) || minutes < 1) {
      return { isValid: false, error: "Sync interval must be at least 1 minute" };
    }
    if (minutes > 1440) {
      return { isValid: false, error: "Sync interval cannot exceed 24 hours (1440 minutes)" };
    }
    return { isValid: true };
  }
  // Validation error management
  setValidationError(key, error) {
    this.validationErrors.set(key, error);
  }
  clearValidationError(key) {
    this.validationErrors.delete(key);
  }
  updateSettingValidation(setting, key) {
    const error = this.validationErrors.get(key);
    if (error) {
      setting.setDesc(`${setting.descEl.textContent} \u26A0\uFE0F ${error}`);
      setting.settingEl.addClass("task-sync-setting-error");
    } else {
      setting.settingEl.removeClass("task-sync-setting-error");
    }
  }
};
