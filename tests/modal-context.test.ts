/**
 * Tests for context-aware modal functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TaskCreateModal } from '../src/components/modals/TaskCreateModal';
import { FileContext } from '../src/main';

// Mock Obsidian API
const mockApp = {
  workspace: {
    getActiveFile: vi.fn()
  },
  vault: {
    create: vi.fn(),
    read: vi.fn()
  }
};

const mockPlugin = {
  app: mockApp,
  settings: {
    tasksFolder: 'Tasks',
    projectsFolder: 'Projects',
    areasFolder: 'Areas',
    templateFolder: 'Templates',
    useTemplater: false,
    defaultTaskTemplate: '',
    defaultProjectTemplate: '',
    defaultAreaTemplate: ''
  }
};



// Setup global mocks
vi.mock('obsidian', () => ({
  Modal: MockModal,
  Setting: MockSetting
}));

describe('TaskCreateModal Context Awareness', () => {
  let modal: TaskCreateModal;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Context Detection', () => {
    it('should handle no context (default)', () => {
      const context: FileContext = { type: 'none' };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);

      expect(modal).toBeDefined();
      // Note: modalEl.addClass calls are made in constructor, but we can't easily test them
      // without more complex mocking. The important thing is the modal is created successfully.
    });

    it('should handle project context', () => {
      const context: FileContext = {
        type: 'project',
        name: 'My Project',
        path: 'Projects/My Project.md'
      };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);

      expect(modal).toBeDefined();
    });

    it('should handle area context', () => {
      const context: FileContext = {
        type: 'area',
        name: 'My Area',
        path: 'Areas/My Area.md'
      };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);

      expect(modal).toBeDefined();
    });
  });

  describe('Modal Title Generation', () => {
    it('should generate default title for no context', () => {
      const context: FileContext = { type: 'none' };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);

      // Access private method for testing
      const title = (modal as any).getContextualTitle();
      expect(title).toBe('Create New Task');
    });

    it('should generate project-specific title', () => {
      const context: FileContext = {
        type: 'project',
        name: 'My Project',
        path: 'Projects/My Project.md'
      };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);

      const title = (modal as any).getContextualTitle();
      expect(title).toBe('Create Task for Project: My Project');
    });

    it('should generate area-specific title', () => {
      const context: FileContext = {
        type: 'area',
        name: 'My Area',
        path: 'Areas/My Area.md'
      };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);

      const title = (modal as any).getContextualTitle();
      expect(title).toBe('Create Task for Area: My Area');
    });
  });

  describe('Form Data Initialization', () => {
    it('should initialize default form data', () => {
      const context: FileContext = { type: 'none' };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);

      (modal as any).initializeFormData();
      const formData = (modal as any).formData;

      expect(formData.name).toBe('');
      expect(formData.type).toBe('Task');
      expect(formData.done).toBe(false);
      expect(formData.status).toBe('Backlog');
      expect(formData.tags).toEqual([]);
    });

    it('should prefill project field for project context', () => {
      const context: FileContext = {
        type: 'project',
        name: 'My Project',
        path: 'Projects/My Project.md'
      };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);

      (modal as any).initializeFormData();
      const formData = (modal as any).formData;

      expect(formData.project).toBe('My Project');
      expect(formData.areas).toBeUndefined();
    });

    it('should prefill areas field for area context', () => {
      const context: FileContext = {
        type: 'area',
        name: 'My Area',
        path: 'Areas/My Area.md'
      };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);

      (modal as any).initializeFormData();
      const formData = (modal as any).formData;

      expect(formData.areas).toBe('My Area');
      expect(formData.project).toBeUndefined();
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      const context: FileContext = { type: 'none' };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);
    });

    it('should validate required task name', async () => {
      (modal as any).formData = { name: '' };

      const showErrorSpy = vi.spyOn(modal as any, 'showError');
      await (modal as any).handleSubmit();

      expect(showErrorSpy).toHaveBeenCalledWith('Task name is required');
    });

    it('should trim task name', async () => {
      (modal as any).formData = { name: '  Test Task  ' };
      (modal as any).onSubmitCallback = vi.fn();

      await (modal as any).handleSubmit();

      expect((modal as any).onSubmitCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Test Task'
        })
      );
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      const context: FileContext = { type: 'none' };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);
    });

    it('should show error message', () => {
      const context: FileContext = { type: 'none' };
      modal = new TaskCreateModal(mockApp as any, mockPlugin as any, context);

      const mockErrorContainer = {
        textContent: '',
        remove: vi.fn(),
        style: {}
      };

      // Mock the contentEl methods for this specific test
      vi.spyOn(modal.contentEl, 'querySelector').mockReturnValue(null);
      vi.spyOn(modal.contentEl, 'createDiv').mockReturnValue(mockErrorContainer);

      (modal as any).showError('Test error message');

      expect(mockErrorContainer.textContent).toBe('Test error message');
    });

    it('should handle submission errors gracefully', async () => {
      (modal as any).formData = { name: 'Test Task' };
      (modal as any).onSubmitCallback = vi.fn().mockRejectedValue(new Error('Submission failed'));

      const showErrorSpy = vi.spyOn(modal as any, 'showError');
      await (modal as any).handleSubmit();

      expect(showErrorSpy).toHaveBeenCalledWith('Failed to create task. Please try again.');
    });
  });
});
