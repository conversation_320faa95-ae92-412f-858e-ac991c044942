/**
 * Mock implementation of Obsidian API for testing
 */

import { vi } from 'vitest';

export class Modal {
  titleEl = { setText: vi.fn() };
  contentEl = { 
    empty: vi.fn(),
    createDiv: vi.fn(() => ({
      createEl: vi.fn(),
      createDiv: vi.fn(),
      addClass: vi.fn()
    })),
    querySelector: vi.fn(),
    createEl: vi.fn()
  };
  modalEl = { addClass: vi.fn() };
  
  constructor(app: any) {
    // Mock constructor
  }
  
  open = vi.fn();
  close = vi.fn();
  onOpen = vi.fn();
  onClose = vi.fn();
}

export class Setting {
  constructor(container: any) {}
  
  setName = vi.fn(() => this);
  setDesc = vi.fn(() => this);
  addText = vi.fn((callback: any) => {
    const mockTextComponent = {
      setPlaceholder: vi.fn(() => mockTextComponent),
      setValue: vi.fn(() => mockTextComponent),
      onChange: vi.fn(() => mockTextComponent),
      setDisabled: vi.fn(() => mockTextComponent),
      inputEl: { addClass: vi.fn(), rows: 4 }
    };
    if (callback) callback(mockTextComponent);
    return this;
  });
  addDropdown = vi.fn((callback: any) => {
    const mockDropdownComponent = {
      addOption: vi.fn(() => mockDropdownComponent),
      setValue: vi.fn(() => mockDropdownComponent),
      onChange: vi.fn(() => mockDropdownComponent)
    };
    if (callback) callback(mockDropdownComponent);
    return this;
  });
  addTextArea = vi.fn((callback: any) => {
    const mockTextAreaComponent = {
      setPlaceholder: vi.fn(() => mockTextAreaComponent),
      setValue: vi.fn(() => mockTextAreaComponent),
      onChange: vi.fn(() => mockTextAreaComponent),
      inputEl: { rows: 4 }
    };
    if (callback) callback(mockTextAreaComponent);
    return this;
  });
  addToggle = vi.fn((callback: any) => {
    const mockToggleComponent = {
      setValue: vi.fn(() => mockToggleComponent),
      onChange: vi.fn(() => mockToggleComponent)
    };
    if (callback) callback(mockToggleComponent);
    return this;
  });
}

export class Plugin {
  app: any;
  manifest: any;
  
  constructor(app: any, manifest: any) {
    this.app = app;
    this.manifest = manifest;
  }
  
  onload = vi.fn();
  onunload = vi.fn();
  loadData = vi.fn();
  saveData = vi.fn();
  addCommand = vi.fn();
  addSettingTab = vi.fn();
}

export class PluginSettingTab {
  app: any;
  plugin: any;
  containerEl = {
    empty: vi.fn(),
    createEl: vi.fn(),
    createDiv: vi.fn(),
    addClass: vi.fn()
  };
  
  constructor(app: any, plugin: any) {
    this.app = app;
    this.plugin = plugin;
  }
  
  display = vi.fn();
  hide = vi.fn();
}

export class TFile {
  path: string;
  name: string;
  extension: string;
  stat: { mtime: number; size: number };
  
  constructor(path: string) {
    this.path = path;
    this.name = path.split('/').pop() || '';
    this.extension = 'md';
    this.stat = { mtime: Date.now(), size: 1000 };
  }
}

export class TFolder {
  path: string;
  name: string;
  children: any[];
  
  constructor(path: string) {
    this.path = path;
    this.name = path.split('/').pop() || '';
    this.children = [];
  }
}

export class Vault {
  getMarkdownFiles = vi.fn(() => []);
  getAbstractFileByPath = vi.fn();
  read = vi.fn();
  create = vi.fn();
  modify = vi.fn();
  delete = vi.fn();
}

export class Workspace {
  getActiveFile = vi.fn();
  getActiveViewOfType = vi.fn();
  on = vi.fn();
  off = vi.fn();
}

export class App {
  vault = new Vault();
  workspace = new Workspace();
  metadataCache = {
    getFileCache: vi.fn(),
    on: vi.fn(),
    off: vi.fn()
  };
}

// Export commonly used types and interfaces
export interface FileSystemAdapter {
  getName(): string;
  path: any;
}

export interface MetadataCache {
  getFileCache(file: TFile): any;
  on(name: string, callback: Function): void;
  off(name: string, callback: Function): void;
}
